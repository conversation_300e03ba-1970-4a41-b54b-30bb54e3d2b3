import os
import pandas as pd

def segregate_excel_files(folder_path, output_file='segregated_output.xlsx'):
    # Create empty DataFrames for each blooms_level
    easy_df = pd.DataFrame()
    medium_df = pd.DataFrame()
    hard_df = pd.DataFrame()

    # Loop through all Excel files in the folder
    for file in os.listdir(folder_path):
        if file.endswith(".xlsx") or file.endswith(".xls"):
            file_path = os.path.join(folder_path, file)
            try:
                df = pd.read_excel(file_path)

                # Ensure 'blooms_level' column exists
                if 'blooms_level' not in df.columns:
                    print(f"Skipped {file} - No 'blooms_level' column found.")
                    continue

                # Append to respective blooms_level-level DataFrames
                easy_df = pd.concat([easy_df, df[df['blooms_level'].str.lower() == ['Remembering','Understanding']]], ignore_index=True)
                medium_df = pd.concat([medium_df, df[df['blooms_level'].str.lower() == 'Appling']], ignore_index=True)
                hard_df = pd.concat([hard_df, df[df['blooms_level'].str.lower() == ['Analyzing', 'Evaluating', 'Creating']]], ignore_index=True)

            except Exception as e:
                print(f"Error reading {file}: {e}")

    # Write to a single Excel file with three sheets
    with pd.ExcelWriter(output_file) as writer:
        easy_df.to_excel(writer, sheet_name='Easy', index=False)
        medium_df.to_excel(writer, sheet_name='Medium', index=False)
        hard_df.to_excel(writer, sheet_name='Hard', index=False)

    print(f"Segregated Excel file saved as: {output_file}")

# Example usage
folder_link = r"D:\lysa_intern'\lysa\Subjects\Chemistry"  # Change this to your actual folder path
segregate_excel_files(folder_link)
