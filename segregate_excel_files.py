import os
import pandas as pd

def segregate_excel_files(folder_path, output_file='.xlsx'):
    # Initialize empty lists to store DataFrames
    easy_dfs = []
    medium_dfs = []
    hard_dfs = []

    # Loop through all Excel files in the folder
    for file in os.listdir(folder_path):
        if file.endswith(".xlsx") or file.endswith(".xls"):
            file_path = os.path.join(folder_path, file)
            try:
                df = pd.read_excel(file_path)

                # Normalize column names by stripping whitespace
                df.columns = [col.strip() for col in df.columns]

                # Ensure 'blooms_level' column exists
                if 'blooms_level' not in df.columns:
                    print(f"Skipped {file} - No 'blooms_level' column found.")
                    continue

                # Filter and collect each level's DataFrame
                easy_dfs.append(df[df['blooms_level'].str.lower().isin(['remembering', 'understanding'])])
                medium_dfs.append(df[df['blooms_level'].str.lower() == 'applying'])
                hard_dfs.append(df[df['blooms_level'].str.lower().isin(['analyzing', 'evaluating', 'creating'])])

            except Exception as e:
                print(f"Error reading {file}: {e}")

    # Concatenate and align columns (pandas auto-aligns by column name)
    easy_df = pd.concat(easy_dfs, ignore_index=True, sort=False)
    medium_df = pd.concat(medium_dfs, ignore_index=True, sort=False)
    hard_df = pd.concat(hard_dfs, ignore_index=True, sort=False)

    # Write to a single Excel file with three sheets
    with pd.ExcelWriter(output_file) as writer:
        easy_df.to_excel(writer, sheet_name='Easy', index=False)
        medium_df.to_excel(writer, sheet_name='Medium', index=False)
        hard_df.to_excel(writer, sheet_name='Hard', index=False)

    print(f"✅ Segregated Excel file saved as: {output_file}")

# Example usage
folder_link = r"D:\lysa_intern'\lysa\Subjects\Geography"  # Corrected path
segregate_excel_files(folder_link)
