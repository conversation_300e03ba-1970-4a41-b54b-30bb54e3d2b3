import os
import pandas as pd

def find_missing_column(folder_path, column_name, output_file='missing_column_files.txt'):
    # List to store file names where the column is missing
    missing_files = []

    # Loop through all Excel files in the folder
    for file in os.listdir(folder_path):
        if file.endswith('.xlsx') or file.endswith('.xls'):
            file_path = os.path.join(folder_path, file)
            try:
                df = pd.read_excel(file_path)
                if column_name not in df.columns:
                    print(f"Column '{column_name}' not found in: {file}")
                    missing_files.append(file)
            except Exception as e:
                print(f"Error reading {file}: {e}")
                missing_files.append(file + f" (Error: {e})")

    # Write the result to a text file
    if missing_files:
        with open(output_file, 'w') as f:
            # f.write(f"Files missing column '{column_name}':\n")
            for file in missing_files:
                f.write(file + '\n')
        print(f"\nList of files missing the column saved to: {output_file}")
    else:
        print(f"✅ All files contain the column '{column_name}'.")

# === Example usage ===
folder_link = r"D:\lysa_intern'\lysa\Subjects\Psychology"  # Change this to your actual folder path
column_to_check = "blooms_level"               # Replace with the column you want to check
find_missing_column(folder_link, column_to_check)
